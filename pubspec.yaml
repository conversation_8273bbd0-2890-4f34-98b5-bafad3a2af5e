name: flutter_basic
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

version: 2.5.16+2516

# flutter version 3.19.5 , dart version  3.3.3 ,devTools 2.31.1
environment:
  sdk: ">=3.0.6 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  dio: 4.0.6
  equatable: 2.0.5
  flutter_bloc: 7.2.0
  bloc: 7.2.1
  logger: ^2.4.0
  device_info_plus: 10.1.0
  synchronized: 3.0.0
  path_provider: 2.1.3
  cupertino_icons: 1.0.2
  locale_plus: 1.5.0
  flutter_localizations:
    sdk: flutter
  flutter_echarts: 2.5.0
  mqtt_client: 10.0.2
  flutter_mxlogger: 1.2.8
  shared_preferences: ^2.2.3 #目前2.2.3支持privacy 但是报错Because flutter_basic depends on shared_preferences 2.2.3 which doesn't match any versions, version solving failed.
  flutter_svg: 2.0.8
  flutter_js: 0.8.0
  http: 1.0.0
  badges: ^3.1.2
  ftoast: 2.0.0
  encrypt: 5.0.3
  flutter_list_view: 1.1.22
  mobile_scanner: 3.5.5
  flutter_screenutil: 5.9.0
  webview_flutter: 4.7.0
  dotted_border: 2.1.0
  dotted_line: 3.2.2
  # 图片压缩
  flutter_image_compress: 2.0.3
  # 图片
  wechat_assets_picker: 9.2.2
  # 图片预览
  photo_view: 0.14.0
  # 图片
  extended_image: 8.2.0
  wechat_camera_picker: 4.2.1
  google_maps_flutter: 2.6.1
  fl_chart: ^0.69.2
  geolocator: 11.0.0
  intl: any
  flutter_smart_dialog: 4.9.5+1
  flutter_switch: 0.3.2
  # 刷新组件
  easy_refresh: 3.3.3+1
  flutter_page_lifecycle: 1.1.0
  connectivity_plus: ^6.0.2
  lottie: 3.1.0
  #蓝牙SDK
  blufi_plugin:
    path: ./lib_blufi
  #-keep class com.signify.hue.** { *; }
  # 蓝牙连接
  flutter_reactive_ble: 5.3.1
  app_settings: 5.1.1
  #动态申请权限
  permission_handler: 11.3.1
  #Json.g
  json_annotation: 4.7.0
  # 解压缩
  archive: ^3.6.1
  flutter_timezone: ^1.0.8
  # app info
  url_launcher: ^6.2.6 #目前6.2.6支持privacy  但是报错 Because flutter_basic depends on url_launcher 6.2.6 which doesn't match any versions, version solving failed.
  #常亮
  wakelock_plus: 1.2.4
  rxdart: 0.27.7
  #firebase
  firebase_core: 2.30.0
  firebase_messaging: 14.8.2
  #本地推送
  flutter_local_notifications: ^17.0.1
  # 跨页面通讯
  event_bus: 2.0.0
  flutter_ume: 1.0.1
  flutter_ume_kit_console: 1.0.0
  flutter_blue_plus: 1.31.16
  crypto: ^3.0.3
  flutter_widget_from_html_core: 0.14.11
  animated_toggle_switch: 0.8.2
  loading_animation_widget: 1.2.1
  package_info_plus: 7.0.0
  timezone: ^0.9.4
  form_builder_validators: 9.1.0
  bloc_concurrency: ^0.1.0
  flutter_form_builder: 9.2.1
  pdf_render: ^1.4.12
  flutter_cache_manager: ^3.3.1
  flutter_keyboard_visibility: ^6.0.0
  share_plus: ^10.0.0
  network_info_plus: ^6.0.1
  multicast_dns: ^0.3.2+7
  nsd: ^4.0.3

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^2.0.0
  build_runner: 2.3.3
  json_serializable: 6.5.4
  iconfont: 1.0.0

dependency_overrides:
  vm_service: 13.0.0 # flutter_ume 与 flutter test 版本兼容(临时方案，等待ume更新)

flutter:
  fonts:
    - family: iconfont # 从 iconfont.json 中获取
      fonts:
        - asset: lib/assets/fonts/iconfont.ttf
    - family: OPPOSans
      fonts:
        - asset: lib/assets/fonts/OPPOSans-B.ttf
          weight: 700
        - asset: lib/assets/fonts/OPPOSans-L.ttf
          weight: 300
        - asset: lib/assets/fonts/OPPOSans-M.ttf
          weight: 500
        - asset: lib/assets/fonts/OPPOSans-R.ttf
          weight: 400
  uses-material-design: true
  assets:
    - lib/assets/images/
    - lib/assets/images_white/
    - lib/assets/js/
    - lib/assets/json/
    - lib/assets/json/country/
    - lib/assets/protocolJS/
    - lib/assets/tsl/
    - lib/assets/ota/
    - lib/assets/icons/

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
