import 'dart:async';

import 'package:flutter_basic/platform/utils/log_utils.dart';
import 'package:nsd/nsd.dart';

import '../model/ecotracker_device_model.dart';

class MdnsScanService {
  static final MdnsScanService _instance = MdnsScanService._internal();

  factory MdnsScanService() => _instance;

  MdnsScanService._internal();

  Discovery? _discovery;
  Timer? _scanTimer;
  final StreamController<List<EcoTrackerDeviceModel>> _devicesController =
      StreamController<List<EcoTrackerDeviceModel>>.broadcast();
  final Map<String, EcoTrackerDeviceModel> _discoveredDevices = {};

  // EcoTracker 设备的 mDNS 服务名称
  static const String _ecoTrackerServiceName = '_everhome._tcp';

  Stream<List<EcoTrackerDeviceModel>> get devicesStream =>
      _devicesController.stream;

  List<EcoTrackerDeviceModel> get currentDevices =>
      _discoveredDevices.values.toList();

  /// 开始扫描设备
  Future<void> startScan(
      {Duration timeout = const Duration(seconds: 30)}) async {
    try {
      logger.d('开始 mDNS 扫描 EcoTracker 设备');
      enableLogging(LogTopic.calls);
      // 停止之前的扫描
      await stopScan();

      // 清空之前的发现结果
      _discoveredDevices.clear();
      _notifyDevicesUpdate();

      // 开始 NSD 服务发现
      logger.d("启动 mDNS 服务发现: $_ecoTrackerServiceName");
      _discovery = await startDiscovery(_ecoTrackerServiceName);
      logger.d("mDNS 服务发现已启动: $_ecoTrackerServiceName");
      // 监听服务发现事件
      _discovery!.addServiceListener(_onServiceDiscovered);

      // 设置超时定时器
      _scanTimer = Timer(timeout, () async {
        logger.d('mDNS 扫描超时，停止扫描');
        await stopScan();
      });
    } catch (e) {
      logger.e('启动 mDNS 扫描失败: $e');
      rethrow;
    }
  }

  /// 停止扫描设备
  Future<void> stopScan() async {
    try {
      _scanTimer?.cancel();
      _scanTimer = null;

      if (_discovery != null) {
        await stopDiscovery(_discovery!);
        _discovery = null;
      }

      logger.d('mDNS 扫描已停止');
    } catch (e) {
      logger.e('停止 mDNS 扫描失败: $e');
    }
  }

  /// 处理服务发现事件
  void _onServiceDiscovered(Service service, ServiceStatus status) {
    logger.d('服务发现事件: ${service.name}, 状态: $status');

    if (status == ServiceStatus.found) {
      _handleServiceFound(service);
    } else if (status == ServiceStatus.lost) {
      _handleServiceLost(service);
    }
  }

  /// 处理发现的服务
  void _handleServiceFound(Service service) {
    try {
      logger
          .d('发现服务: ${service.name}, 主机: ${service.host}, 端口: ${service.port}');

      // 获取 IP 地址
      String ipAddress = '';
      if (service.addresses?.isNotEmpty == true) {
        final firstAddress = service.addresses!.first;
        ipAddress = firstAddress.host.toString();
      }

      // 转换 TXT 记录
      Map<String, String>? txtRecords;
      if (service.txt != null) {
        txtRecords = <String, String>{};
        service.txt!.forEach((key, value) {
          if (value != null) {
            txtRecords![key] = String.fromCharCodes(value);
          }
        });
      }

      // 创建设备模型
      final device = EcoTrackerDeviceModel.fromMdnsRecord(
        serviceName: service.name ?? '',
        hostname: service.host ?? '',
        ipAddress: ipAddress,
        port: service.port ?? 0,
        txtRecords: txtRecords,
      );

      final deviceId = service.name ?? '';
      _discoveredDevices[deviceId] = device;
      _notifyDevicesUpdate();
    } catch (e) {
      logger.e('处理发现的服务失败: $e');
    }
  }

  /// 处理丢失的服务
  void _handleServiceLost(Service service) {
    try {
      final deviceId = service.name ?? '';
      if (_discoveredDevices.containsKey(deviceId)) {
        _discoveredDevices.remove(deviceId);
        _notifyDevicesUpdate();
        logger.d('移除丢失的服务: $deviceId');
      }
    } catch (e) {
      logger.e('处理丢失的服务失败: $e');
    }
  }

  /// 通知设备列表更新
  void _notifyDevicesUpdate() {
    if (!_devicesController.isClosed) {
      logger.d('通知设备列表更新: 当前有 ${currentDevices.length} 个设备');
      _devicesController.add(currentDevices);
    }
  }

  /// 清空发现的设备
  void clearDevices() {
    _discoveredDevices.clear();
    _notifyDevicesUpdate();
  }

  /// 释放资源
  void dispose() {
    stopScan();
    _devicesController.close();
  }
}
