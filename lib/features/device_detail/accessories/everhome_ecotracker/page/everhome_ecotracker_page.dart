import 'package:flutter_basic/components/custom_card.dart';
import 'package:flutter_basic/components/custom_image_asset.dart';
import 'package:flutter_basic/features/diy_new/components/water_ripples.dart';
import 'package:flutter_basic/generated/l10n.dart';
import 'package:flutter_basic/platform/platform.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../bloc/ecotracker_scan_bloc.dart';
import '../bloc/ecotracker_scan_event.dart';
import '../bloc/ecotracker_scan_state.dart';

class EverHomeEcotrackerPage extends StatefulWidget {
  const EverHomeEcotrackerPage({super.key});

  @override
  State<EverHomeEcotrackerPage> createState() => _EverHomeEcotrackerPageState();
}

class _EverHomeEcotrackerPageState extends State<EverHomeEcotrackerPage> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => EcoTrackerScanBloc()..add(const StartScanEvent()),
      child: Builder(
        builder: (context) => Scaffold(
          backgroundColor: ColorsUtil.backgroundColor,
          appBar: CustomAppBar(
            title: AppBarCenterText(
              title: 'EverHome Ecotracker', // TODO: Add translation key
            ),
          ),
          body: Column(
            children: [
              Expanded(
                child: BlocConsumer<EcoTrackerScanBloc, EcoTrackerScanState>(
                  listener: (context, state) {
                    if (state.connectStatus == ConnectStatus.connected) {
                      CustomToast.showToast(context,
                          'Device connected successfully!'); // TODO: Add translation key
                    } else if (state.connectStatus == ConnectStatus.failed) {
                      CustomToast.showToast(
                          context,
                          state.error ??
                              'Device connection failed'); // TODO: Add translation key
                    }
                  },
                  builder: (BuildContext context, EcoTrackerScanState state) {
                    return Column(
                      children: [
                        Container(
                          padding: EdgeInsets.only(top: 20.w, left: 20.w),
                          alignment: Alignment.centerLeft,
                          child: Text(
                            state.devices.isEmpty
                                ? 'Searching for EcoTracker devices...' // TODO: Add translation key
                                : '${state.devices.length} devices found', // TODO: Add translation key
                            style: TextStyle(
                                fontSize: 14.sp, color: ColorsUtil.hintColor),
                          ),
                        ),
                        _buildScanView(state, context),
                        Expanded(
                          child: Container(
                            child: state.devices.isEmpty
                                ? _emptyView()
                                : Padding(
                                    padding: EdgeInsets.only(
                                        left: 20.w, right: 20.w),
                                    child: _buildDeviceList(state, context),
                                  ),
                          ),
                        )
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建扫描视图 - 参考 add_shelly_page 结构
  Widget _buildScanView(EcoTrackerScanState state, BuildContext context) {
    return CustomCard(
        margin:
            EdgeInsets.only(left: 20.w, top: 10.w, bottom: 20.w, right: 20.w),
        child: SizedBox(
          height: 44.w,
          child: Row(
            children: [
              const WaterRipples(),
              SizedBox(
                width: 10.w,
              ),
              state.scanStatus == ScanStatus.scanning
                  ? Text(S.current.text('scan.device.scanning'),
                      style: TextStyle(
                          fontSize: 13.sp, color: ColorsUtil.themeColor))
                  : Text(S.current.text('scan.device.scan'),
                      style: TextStyle(
                          fontSize: 13.sp, color: ColorsUtil.themeColor)),
              Expanded(child: Container()),
              state.scanStatus == ScanStatus.scanning
                  ? Padding(
                      padding: EdgeInsets.only(right: 20.w),
                      child: Text('${state.remainingTime} s',
                          style: TextStyle(
                              fontSize: 13.sp, color: ColorsUtil.hintColor)),
                    )
                  : Container(
                      height: 24.w,
                      margin: EdgeInsets.only(right: 10.w),
                      child: ElevatedButton(
                          style: ButtonStyle(
                            backgroundColor: MaterialStateProperty.all(
                                ColorsUtil.themeColor),
                            shape: MaterialStateProperty.all(
                                RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(22.w))),
                            shadowColor: MaterialStateProperty.all(
                              ColorsUtil.transparentColor,
                            ),
                          ),
                          onPressed: () {
                            context
                                .read<EcoTrackerScanBloc>()
                                .add(const StartScanEvent());
                          },
                          child: Text(
                            S.current.text('scan.device.rescan'),
                            style: TextStyle(
                                fontSize: 10.sp,
                                color: ColorsUtil.buttonTextColor),
                          )),
                    )
            ],
          ),
        ));
  }

  /// 空状态视图 - 参考 add_shelly_page 结构
  Widget _emptyView() {
    return Column(
      children: [
        SizedBox(
          height: 55.h,
        ),
        Icon(
          Icons.devices_other,
          size: 65.w,
          color: ColorsUtil.hintColor.withOpacity(0.5),
        ),
        Text(
          S.current.text('scan.device.not_found'),
          textAlign: TextAlign.center,
          style: TextStyle(
              fontSize: 13.sp, color: ColorsUtil.hintColor, height: 1.5),
        )
      ],
    );
  }

  /// 设备列表 - 参考 add_shelly_page 使用 GridView 结构
  Widget _buildDeviceList(EcoTrackerScanState state, BuildContext context) {
    return GridView.builder(
        itemCount: state.devices.length,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 10.w,
            mainAxisSpacing: 10.h,
            childAspectRatio: 1.3), // 调整高度比例以容纳更多信息
        itemBuilder: (context, index) {
          var device = state.devices[index];
          final isConnected = state.connectedDeviceId == device.id;

          return InkWell(
            onTap: () {
              context.read<EcoTrackerScanBloc>().add(
                    ConnectDeviceEvent(
                      deviceId: device.id,
                      deviceName: device.name,
                      ipAddress: device.ipAddress,
                      port: device.port,
                    ),
                  );
            },
            child: CustomCard(
              child: Container(
                padding: EdgeInsets.only(
                    left: 10.w, right: 5.w, top: 8.w, bottom: 8.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 60.w,
                          height: 60.w,
                          child: CustomImageAsset(
                            'icon_everhome_ecotracker',
                          ),
                        ),
                        Expanded(child: Container()),
                        Container(
                            padding: EdgeInsets.only(
                                left: 8.w, right: 8.w, top: 2.w, bottom: 2.w),
                            decoration: BoxDecoration(
                                color: Colors.grey.withOpacity(0.1),
                                borderRadius: BorderRadius.all(
                                  Radius.circular(9.w),
                                )),
                            child: isConnected
                                ? Text(
                                    'Connected', // TODO: Add translation key
                                    style: TextStyle(
                                        fontSize: 10.sp,
                                        color: ColorsUtil.systemAlarmColor),
                                  )
                                : Text(
                                    'Not Connected', // TODO: Add translation key
                                    style: TextStyle(
                                        fontSize: 10.sp,
                                        color: ColorsUtil.hintColor),
                                  )),
                      ],
                    ),
                    // 使用 Expanded 让底部内容贴底显示
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            device.serviceName?.replaceAll("ecotracker-", "") ??
                                device.name,
                            style: TextStyle(
                                fontSize: 13.sp,
                                color: ColorsUtil.textColor,
                                fontWeight: FontWeight.w600),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(
                            height: 2.w,
                          ),
                          // 显示IP地址
                          Text(
                            device.ipAddress,
                            style: TextStyle(
                              fontSize: 11.sp,
                              color: ColorsUtil.hintColor,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        });
  }
}
